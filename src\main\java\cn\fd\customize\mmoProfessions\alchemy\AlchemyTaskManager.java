package cn.fd.customize.mmoProfessions.alchemy;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitTask;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 炼金任务管理器
 * 管理所有炼金任务的创建、执行、完成和数据持久化
 */
public class AlchemyTaskManager {

    private final Map<UUID, Map<Integer, AlchemyTask>> playerTasks; // 玩家ID -> 任务列表
    private final Map<UUID, Integer> playerUnlockedQueues; // 玩家ID -> 解锁的队列数量
    private BukkitTask globalTaskCheckTask; // 全局任务检查定时器

    public AlchemyTaskManager() {
        this.playerTasks = new ConcurrentHashMap<>();
        this.playerUnlockedQueues = new ConcurrentHashMap<>();
        startGlobalTaskCheck();
    }

    /**
     * 创建新的炼金任务
     */
    public AlchemyTask createTask(Player player, int queueIndex, List<ItemStack> inputItems,
            ItemStack stabilizer, List<ItemStack> outputItems, long duration) {

        // 检查队列是否解锁
        if (!isQueueUnlocked(player.getUniqueId(), queueIndex)) {
            throw new IllegalArgumentException("队列 " + queueIndex + " 尚未解锁");
        }

        AlchemyTask task = new AlchemyTask(queueIndex, inputItems, stabilizer, duration);

        // 生成输出物品
        task.setOutputItems(outputItems);

        // 添加到管理器
        playerTasks.computeIfAbsent(player.getUniqueId(), k -> new HashMap<>()).put(queueIndex, task);

        // 立即保存
        MMOProfessions.getDataStorage().savePlayerData(player.getUniqueId());

        MMOProfessions.getInstance().getLogger().info(String.format("创建炼金任务: %d (玩家: %s, 队列: %d, 持续时间: %ds)",
                task.getTaskId(), player.getName(), queueIndex, duration / 1000));

        return task;
    }

    /**
     * 获取玩家的所有任务
     */
    public Map<Integer, AlchemyTask> getPlayerTasks(UUID playerId) {
        return playerTasks.computeIfAbsent(playerId,
                k -> MMOProfessions.getDataStorage().loadPlayerData(playerId).alchemy_tasks);
    }

    /**
     * 获取玩家解锁的队列数量
     */
    public int getUnlockedQueueCount(UUID playerId) {
        return playerUnlockedQueues.computeIfAbsent(playerId, k -> {
            // 从数据文件加载
            int count = MMOProfessions.getDataStorage().loadPlayerData(playerId).unlocked_queue_count;
            if (count <= 0) {
                // 如果没有数据，使用默认基础队列数
                count = MMOProfessions.getAlchemyManager().getConfigManager().getBaseQueueCount();
            }
            return count;
        });
    }

    /**
     * 检查玩家是否解锁了指定队列
     */
    public boolean isQueueUnlocked(UUID playerId, int queueIndex) {
        return queueIndex < getUnlockedQueueCount(playerId);
    }

    /**
     * 解锁玩家的下一个队列
     */
    public boolean unlockNextQueue(UUID playerId) {
        int currentCount = getUnlockedQueueCount(playerId);
        int maxCount = MMOProfessions.getAlchemyManager().getConfigManager().getMaxQueueCount();

        if (currentCount >= maxCount) {
            return false; // 已达到最大队列数
        }

        playerUnlockedQueues.put(playerId, currentCount + 1);
        MMOProfessions.getDataStorage().savePlayerData(playerId);
        return true;
    }

    /**
     * 设置玩家解锁的队列数量
     */
    public void setUnlockedQueueCount(UUID playerId, int count) {
        int maxCount = MMOProfessions.getAlchemyManager().getConfigManager().getMaxQueueCount();
        count = Math.max(1, Math.min(count, maxCount)); // 限制在1到最大值之间

        playerUnlockedQueues.put(playerId, count);
        MMOProfessions.getDataStorage().savePlayerData(playerId);
    }

    /**
     * 获取玩家的指定队列任务
     */
    public AlchemyTask getPlayerQueueTask(UUID playerId, int queueIndex) {
        return getPlayerTasks(playerId).get(queueIndex);
    }

    /**
     * 添加加载过的任务
     */
    public void addLoadedTask(UUID playerId, AlchemyTask task) {
        playerTasks.computeIfAbsent(playerId, k -> new HashMap<>()).put(task.getTaskId(), task);
    }

    /**
     * 取消任务
     */
    public boolean cancelTask(UUID playerId, int taskId) {
        AlchemyTask task = getPlayerQueueTask(playerId, taskId);
        if (task != null && !task.isCompleted()) {
            // 删除任务
            playerTasks.get(playerId).remove(taskId);
            return true;
        }
        return false;
    }

    /**
     * 删除已完成且无输出物品的任务
     */
    public boolean removeCompletedEmptyTask(UUID playerId, int queueIndex) {
        AlchemyTask task = getPlayerQueueTask(playerId, queueIndex);
        if (task != null && task.isCompleted() && task.getOutputItems().isEmpty()) {
            playerTasks.get(playerId).remove(queueIndex);
            MMOProfessions.getDataStorage().savePlayerData(playerId);
            return true;
        }
        return false;
    }

    /**
     * 启动全局任务检查定时器
     */
    private void startGlobalTaskCheck() {
        globalTaskCheckTask = Bukkit.getScheduler().runTaskTimer(MMOProfessions.getInstance(), () -> {
            // 检查所有玩家的任务
            for (Map.Entry<UUID, Map<Integer, AlchemyTask>> playerEntry : playerTasks.entrySet()) {
                UUID playerId = playerEntry.getKey();
                Player player = Bukkit.getPlayer(playerId);

                for (AlchemyTask task : playerEntry.getValue().values()) {
                    // 检查任务是否刚完成且未被处理过
                    if (task.isCompleted() && !task.isOutputProcessed()) {
                        if (!task.getOutputItems().isEmpty()) {
                            // 任务完成且有输出物品
                            if (player != null && player.isOnline()) {
                                // 玩家在线，发送完成通知（队列索引显示时从1开始）
                                MMOProfessions.getMessageManager().sendMessage(player, "alchemy.task_completed",
                                        String.valueOf(task.getTaskId() + 1));
                            }
                        } else {
                            // 任务完成但失败（无输出物品）
                            if (player != null && player.isOnline()) {
                                // 玩家在线，发送失败通知（队列索引显示时从1开始）
                                MMOProfessions.getMessageManager().sendMessage(player, "alchemy.task_failed",
                                        String.valueOf(task.getTaskId() + 1));
                            }
                        }

                        // 标记输出已处理
                        task.markOutputProcessed();

                        // 保存数据
                        MMOProfessions.getDataStorage().savePlayerData(playerId);
                    }
                }
            }
        }, 20L, 20L); // 每秒检查一次
    }

    /**
     * 停止全局任务检查定时器
     */
    public void stopGlobalTaskCheck() {
        if (globalTaskCheckTask != null) {
            globalTaskCheckTask.cancel();
            globalTaskCheckTask = null;
        }
    }



    /**
     * 检查并删除已完成且无输出物品的任务
     * @param playerId 玩家ID
     * @return 删除的任务数量
     */
    public int checkAndRemoveCompletedEmptyTasks(UUID playerId) {
        Map<Integer, AlchemyTask> playerTasks = getPlayerTasks(playerId);
        List<Integer> tasksToRemove = new ArrayList<>();

        for (Map.Entry<Integer, AlchemyTask> entry : playerTasks.entrySet()) {
            AlchemyTask task = entry.getValue();
            // 检查任务是否完成且无输出物品
            if (task.isCompleted() && task.getOutputItems().isEmpty()) {
                tasksToRemove.add(entry.getKey());
            }
        }

        // 删除符合条件的任务
        for (Integer queueIndex : tasksToRemove) {
            removeCompletedEmptyTask(playerId, queueIndex);
        }

        return tasksToRemove.size();
    }

}
