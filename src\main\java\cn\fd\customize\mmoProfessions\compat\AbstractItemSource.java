package cn.fd.customize.mmoProfessions.compat;

import org.bukkit.inventory.ItemStack;

/**
 * 物品源抽象基类
 * 提供通用的解析和处理逻辑
 */
public abstract class AbstractItemSource implements ItemSource {

    protected final ItemSourceFactory factory;
    protected final String value;
    protected final int[] amountRange;

    public AbstractItemSource(ItemSourceFactory factory, String value, String amount) {
        this.factory = factory;
        this.value = value;
        this.amountRange = ItemParser.parseAmount(amount);
    }

    @Override
    public ItemSourceFactory getFactory() {
        return factory;
    }

    @Override
    public String getValue() {
        return value;
    }

    protected ItemStack applyAmount(ItemStack itemStack) {
        int amount = ItemParser.randomAmount(amountRange);
        itemStack.setAmount(amount);
        return itemStack;
    }

    @Override
    public String toString() {
        return String.format("%s{source=%s, value=%s}",
                getClass().getSimpleName(), factory.getSourceType(), value);
    }

}
