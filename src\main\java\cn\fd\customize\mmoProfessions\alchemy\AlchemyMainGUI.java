package cn.fd.customize.mmoProfessions.alchemy;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.config.AlchemyConfigLoader;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;

/**
 * 炼金主界面GUI
 * 包含输入框、确认按钮、队列按钮、输出框、稳定剂槽等
 */
public class AlchemyMainGUI {

    private final AlchemyManager alchemyManager;
    private final Map<UUID, Inventory> playerInventories;

    // 配置项
    private String title;

    private List<Integer> inputSlots;
    private List<Integer> outputSlots;
    private List<Integer> queueButtonSlots;
    private int stabilizerSlot;
    private int confirmButtonSlot;
    private List<Integer> fillerSlots;
    private ItemStack confirmButton;
    private ItemStack queueActiveButton;
    private ItemStack queueIdleButton;
    private ItemStack queueLockedButton;
    private ItemStack fillerItem;

    // 玩家输出物品队列 - 存储等待显示的输出物品
    private final Map<UUID, List<ItemStack>> playerOutputQueue;

    // 队列按钮刷新任务
    private static BukkitTask queueRefreshTask;

    public AlchemyMainGUI(AlchemyManager alchemyManager) {
        this.alchemyManager = alchemyManager;
        this.playerInventories = new HashMap<>();
        this.playerOutputQueue = new HashMap<>();

        loadConfiguration();
    }

    /**
     * 启动队列按钮刷新定时器
     */
    public void startQueueRefreshTask() {
        // 启动新的刷新任务
        queueRefreshTask = Bukkit.getScheduler().runTaskTimer(MMOProfessions.getInstance(), () -> {
            // 遍历所有打开炼金界面的在线玩家
            for (Map.Entry<UUID, Inventory> entry : playerInventories.entrySet()) {
                UUID playerId = entry.getKey();
                Inventory inventory = entry.getValue();

                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    // 检查玩家是否还在查看炼金界面
                    if (isAlchemyMainGUI(player, player.getOpenInventory().getTopInventory())) {
                        // 检查并处理完成的任务
                        if (checkAndProcessCompletedTasks(player)) {
                            updateOutputSlots(player, inventory);
                            MMOProfessions.getDataStorage().savePlayerData(player.getUniqueId());
                        }
                        // 更新队列按钮
                        updateQueueButtons(inventory, player);
                    }
                }
            }
        }, 20L, 20L); // 每秒刷新一次
    }

    public void cancelQueueRefreshTimer() {
        if (queueRefreshTask != null) {
            queueRefreshTask.cancel();
        }
    }

    /**
     * 加载配置
     */
    private void loadConfiguration() {
        AlchemyConfigLoader configManager = alchemyManager.getConfigManager();

        // 直接从配置文件获取配置项
        title = configManager.getConfig().getString("gui.title");
        inputSlots = configManager.getConfig().getIntegerList("gui.input_slots");
        outputSlots = configManager.getConfig().getIntegerList("gui.output_slots");
        stabilizerSlot = configManager.getConfig().getInt("gui.stabilizer_slot");
        queueButtonSlots = configManager.getQueueButtonSlots();
        confirmButtonSlot = configManager.getConfig().getInt("gui.confirm_button_slot");
        fillerSlots = configManager.getConfig().getIntegerList("gui.filler_slots");

        // 加载按钮物品
        confirmButton = configManager.createItemFromConfig("gui.buttons.confirm");
        queueActiveButton = configManager.createItemFromConfig("gui.buttons.queue_active");
        queueIdleButton = configManager.createItemFromConfig("gui.buttons.queue_idle");
        queueLockedButton = configManager.createItemFromConfig("gui.buttons.queue_locked");
        fillerItem = configManager.createItemFromConfig("gui.buttons.filler");
    }

    /**
     * 打开炼金界面
     */
    public void openGUI(Player player) {
        Inventory inventory = Bukkit.createInventory(null, 54, title);

        // 设置填充物品
        for (int slot : fillerSlots) {
            if (slot < 0 || slot > 53
                    || outputSlots.contains(slot)
                    || inputSlots.contains(slot)
                    || stabilizerSlot == slot) {
                continue;
            }
            inventory.setItem(slot, fillerItem.clone());
        }

        // 设置确认按钮
        inventory.setItem(confirmButtonSlot, confirmButton.clone());

        // 设置队列按钮
        updateQueueButtons(inventory, player);

        // 恢复玩家之前的物品（如果有）
        restorePlayerItems(inventory, player);

        playerInventories.put(player.getUniqueId(), inventory);
        player.openInventory(inventory);
    }

    /**
     * 更新输出槽显示
     */
    private void updateOutputSlots(Player player, Inventory inventory) {
        List<ItemStack> outputQueue = playerOutputQueue.get(player.getUniqueId());
        if (outputQueue == null || outputQueue.isEmpty()) {
            return;
        }

        // 检查输出槽位，将队列中的物品放入空槽位
        for (int slot : outputSlots) {
            ItemStack currentItem = inventory.getItem(slot);
            if ((currentItem == null || currentItem.getType().isAir()) && !outputQueue.isEmpty()) {
                ItemStack nextItem = outputQueue.remove(0);
                inventory.setItem(slot, nextItem);
            }
        }

        // 如果队列为空，移除
        if (outputQueue.isEmpty()) {
            playerOutputQueue.remove(player.getUniqueId());
        }
    }

    /**
     * 更新队列按钮
     */
    private void updateQueueButtons(Inventory inventory, Player player) {
        for (int queueIndex = 0; queueIndex < queueButtonSlots.size(); queueIndex++) {
            int slot = queueButtonSlots.get(queueIndex);

            // 获取队列任务
            AlchemyTask task = alchemyManager.getTaskManager().getPlayerQueueTask(player.getUniqueId(), queueIndex);

            ItemStack button;

            // 检查队列是否解锁
            if (!alchemyManager.getTaskManager().isQueueUnlocked(player.getUniqueId(), queueIndex)) {
                // 未解锁的队列
                button = queueLockedButton.clone();
            } else {

                if (task != null && !task.isCompleted()) {
                    // 进行中的任务
                    button = queueActiveButton.clone();
                } else {
                    // 空闲队列
                    button = queueIdleButton.clone();
                }
            }

            // 替换占位符
            if (button.hasItemMeta()) {
                ItemMeta meta = button.getItemMeta();
                if (meta.hasDisplayName()) {
                    meta.setDisplayName(replacePlaceholders(meta.getDisplayName(), task, queueIndex));
                }
                if (meta.hasLore()) {
                    List<String> newLore = new ArrayList<>();
                    for (String line : meta.getLore()) {
                        newLore.add(replacePlaceholders(line, task, queueIndex));
                    }
                    meta.setLore(newLore);
                }
                button.setItemMeta(meta);
            }

            inventory.setItem(slot, button);
        }
    }

    /**
     * 恢复玩家物品
     */
    private void restorePlayerItems(Inventory inventory, Player player) {
        // 检查是否有等待显示的输出物品队列
        List<ItemStack> outputQueue = playerOutputQueue.get(player.getUniqueId());
        if (outputQueue != null && !outputQueue.isEmpty()) {
            // 显示输出物品到输出槽位
            int slotIndex = 0;
            Iterator<ItemStack> iterator = outputQueue.iterator();
            while (iterator.hasNext() && slotIndex < outputSlots.size()) {
                ItemStack output = iterator.next();
                inventory.setItem(outputSlots.get(slotIndex), output.clone());
                iterator.remove(); // 从队列中移除已显示的物品
                slotIndex++;
            }

            // 如果队列为空，移除
            if (outputQueue.isEmpty()) {
                playerOutputQueue.remove(player.getUniqueId());
            }
        }

        // 检查并处理新完成的任务
        if (checkAndProcessCompletedTasks(player)) {
            updateOutputSlots(player, inventory);
            MMOProfessions.getDataStorage().savePlayerData(player.getUniqueId());
        }
    }

    /**
     * 检查是否是玩家的炼金界面
     */
    public boolean isAlchemyMainGUI(Player player, Inventory inventory) {
        Inventory playerInv = playerInventories.get(player.getUniqueId());
        return playerInv != null && playerInv.equals(inventory);
    }

    /**
     * 处理界面点击事件
     */
    public void handleInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        Inventory inventory = playerInventories.get(player.getUniqueId());

        if (inventory == null || !event.getInventory().equals(inventory)) {
            return;
        }

        int slot = event.getRawSlot();

        // 检查是否不在此界面内点击
        if (slot < 0 || slot > 53) {
            return;
        }

        // 允许在输入槽位和稳定剂槽位进行操作
        if (inputSlots.contains(slot) || stabilizerSlot == slot) {
            return;
        }

        // 检查是否点击了填充物品槽位
        if (fillerSlots.contains(slot)) {
            event.setCancelled(true);
            return;
        }

        // 检查是否点击了队列按钮
        if (queueButtonSlots.contains(slot)) {
            event.setCancelled(true);
            int queueIndex = queueButtonSlots.indexOf(slot);
            handleQueueClick(player, queueIndex);
            return;
        }

        // 检查是否点击输出槽
        if (outputSlots.contains(slot)) {
            handleOutputClick(player, inventory, slot, event);
            return;
        }

        // 检查是否点击了功能按钮
        if (slot == confirmButtonSlot) {
            event.setCancelled(true);
            handleConfirmClick(player, inventory);
            return;
        }

        // 其他所有槽位（包括未设置物品的空位）都禁止操作
        event.setCancelled(true);
    }

    /**
     * 处理确认按钮点击
     */
    private void handleConfirmClick(Player player, Inventory inventory) {
        // 收集输入物品
        List<ItemStack> inputItems = new ArrayList<>();
        for (int slot : inputSlots) {
            ItemStack item = inventory.getItem(slot);
            if (item != null && !item.getType().isAir()) {
                inputItems.add(item.clone());
            }
        }

        if (inputItems.isEmpty()) {
            MMOProfessions.getMessageManager().sendMessage(player, "alchemy.no_input_items");
            return;
        }

        // 收集稳定剂
        ItemStack stabilizerItem = inventory.getItem(stabilizerSlot);
        if (stabilizerItem != null && stabilizerItem.getType().isAir()) {
            stabilizerItem = null;
        }

        // 匹配配方
        AlchemyRecipe recipe = alchemyManager.getRecipeManager().matchRecipe(inputItems,
                stabilizerItem != null ? Arrays.asList(stabilizerItem) : new ArrayList<>(), player);
        if (recipe == null) {
            MMOProfessions.getMessageManager().sendMessage(player, "alchemy.no_recipe_found");
            return;
        }

        // 获取玩家等级
        int playerLevel = MMOProfessions.getProfessionManager().getPlayerData(player).getLevel("alchemy");

        // 获取适配的等级选择
        LevelChoice choice = recipe.getAdaptedChoice(playerLevel);
        if (choice == null) {
            MMOProfessions.getMessageManager().sendMessage(player, "alchemy.level_insufficient");
            return;
        }

        // 计算输出物品和时间
        List<ItemStack> outputItems = recipe.calculateOutput(inputItems, stabilizerItem, player);
        long duration = choice.getDuration() * 1000; // 转换为毫秒

        // 选择队列（选择任务最少的队列）
        int selectedQueue = selectBestQueue(player);

        // 创建任务
        AlchemyTask task = alchemyManager.getTaskManager().createTask(player, selectedQueue, inputItems,
                stabilizerItem, outputItems, duration);

        // 清空输入槽和稳定剂槽
        for (int slot : inputSlots) {
            inventory.setItem(slot, null);
        }
        inventory.setItem(stabilizerSlot, null);

        // 更新队列按钮
        updateQueueButtons(inventory, player);

        String formattedTime = alchemyManager.getConfigManager().formatRemainingTime(task.getRemainingTime());
        MMOProfessions.getMessageManager().sendMessage(player, "alchemy.task_created",
                String.valueOf(selectedQueue + 1), formattedTime); // 队列索引显示时从1开始
    }

    /**
     * 处理队列按钮点击
     */
    private void handleQueueClick(Player player, int queueIndex) {
        // 检查队列是否解锁
        if (!alchemyManager.getTaskManager().isQueueUnlocked(player.getUniqueId(), queueIndex)) {
            player.sendMessage("§c队列 " + queueIndex + " 尚未解锁！");
            player.sendMessage("§7使用 §e/alchemy unlock " + queueIndex + " §7解锁此队列");
            return;
        }

        // 队列按钮主要用于显示状态，点击时显示队列信息
        AlchemyTask task = alchemyManager.getTaskManager().getPlayerQueueTask(player.getUniqueId(), queueIndex);

        if (task != null && !task.isCompleted()) {
            String timeRemaining = alchemyManager.getConfigManager().formatRemainingTime(task.getRemainingTime());
            player.sendMessage("§e队列 " + queueIndex + " 状态: §a进行中");
            player.sendMessage("§7剩余时间: §e" + timeRemaining);
        } else {
            player.sendMessage("§e队列 " + queueIndex + " 状态: §7空闲");
        }
    }

    /**
     * 处理输出槽点击
     */
    private void handleOutputClick(Player player, Inventory inventory, int slot, InventoryClickEvent event) {
        // 只允许拿出物品的操作，禁止放入物品
        switch (event.getAction()) {
            case PICKUP_ONE:
            case PICKUP_ALL:
            case PICKUP_HALF:
            case PICKUP_SOME:
            case MOVE_TO_OTHER_INVENTORY: // Shift+点击移动到背包
                // 允许这些拿出物品的操作
                break;
            default:
                // 禁止其他所有操作（包括拖拽放入、交换等）
                event.setCancelled(true);
                return;
        }

        // 玩家拿出物品后，检查是否有等待显示的输出物品
        Bukkit.getScheduler().runTaskLater(MMOProfessions.getInstance(), () -> {
            // 检查槽位是否已经被清空
            if (inventory.getItem(slot) == null || inventory.getItem(slot).getType().isAir()) {
                List<ItemStack> outputQueue = playerOutputQueue.get(player.getUniqueId());
                if (outputQueue != null && !outputQueue.isEmpty()) {
                    // 从队列中取出第一个物品放入刚被拿走的槽位
                    ItemStack nextItem = outputQueue.remove(0);
                    inventory.setItem(slot, nextItem);

                    // 如果队列为空，移除玩家的输出队列
                    if (outputQueue.isEmpty()) {
                        playerOutputQueue.remove(player.getUniqueId());
                    }

                    // 立即保存玩家数据
                    MMOProfessions.getDataStorage().savePlayerData(player.getUniqueId());
                }

                // 检查并删除已完成且无输出物品的任务
                checkAndRemoveCompletedEmptyTasks(player);
            }
        }, 1L); // 延迟1tick执行，确保物品已被拿走
    }

    /**
     * 处理界面关闭事件
     */
    public void handleInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();
        Inventory inventory = playerInventories.remove(player.getUniqueId());

        if (inventory != null) {
            // 返还输入槽和稳定剂槽中的物品
            returnItems(player, inventory);
        }
    }

    /**
     * 处理拖拽事件
     */
    public void handleInventoryDrag(InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        Inventory inventory = playerInventories.get(player.getUniqueId());

        if (inventory == null || !event.getInventory().equals(inventory)) {
            return;
        }

        // 检查拖拽的槽位，只允许在输入槽位和稳定剂槽位进行拖拽
        for (int slot : event.getRawSlots()) {
            // 如果拖拽涉及界面内的槽位，但不是输入槽位或稳定剂槽位，则取消事件
            if (slot >= 0 && slot <= 53) {
                if (!inputSlots.contains(slot) && slot != stabilizerSlot) {
                    event.setCancelled(true);
                    return;
                }
            }
        }
    }

    /**
     * 检查并删除已完成且无输出物品的任务
     */
    private void checkAndRemoveCompletedEmptyTasks(Player player) {
        // 使用TaskManager中的方法处理任务删除
        int removedCount = alchemyManager.getTaskManager()
                .checkAndRemoveCompletedEmptyTasks(player.getUniqueId());

        // 如果删除了任务，更新队列按钮显示
        if (removedCount > 0) {
            Inventory inventory = playerInventories.get(player.getUniqueId());
            if (inventory != null) {
                updateQueueButtons(inventory, player);
            }
        }
    }

    /**
     * 检查并处理完成的任务（用于GUI中的输出物品显示）
     */
    private boolean checkAndProcessCompletedTasks(Player player) {
        Map<Integer, AlchemyTask> playerTasks = alchemyManager.getTaskManager().getPlayerTasks(player.getUniqueId());
        boolean hasNewOutputs = false;

        for (AlchemyTask task : playerTasks.values()) {
            // 检查任务是否完成且有输出物品且已被全局检查处理过
            if (task.isCompleted() && !task.getOutputItems().isEmpty() && task.isOutputProcessed()) {
                // 任务已完成且有输出物品，添加到输出队列
                List<ItemStack> outputQueue = playerOutputQueue.computeIfAbsent(player.getUniqueId(),
                        k -> new ArrayList<>());
                outputQueue.addAll(task.getOutputItems());

                // 清空任务的输出物品，避免重复添加到队列
                task.setOutputItems(new ArrayList<>());
                hasNewOutputs = true;
            }
        }

        return hasNewOutputs;
    }

    /**
     * 返还物品给玩家
     */
    private void returnItems(Player player, Inventory inventory) {
        List<ItemStack> itemsToReturn = new ArrayList<>();

        // 收集输入槽物品
        for (int slot : inputSlots) {
            ItemStack item = inventory.getItem(slot);
            if (item != null && !item.getType().isAir()) {
                itemsToReturn.add(item);
            }
        }

        // 收集稳定剂槽物品
        ItemStack stabilizerItem = inventory.getItem(stabilizerSlot);
        if (stabilizerItem != null && !stabilizerItem.getType().isAir()) {
            itemsToReturn.add(stabilizerItem);
        }

        // 返还物品
        for (ItemStack item : itemsToReturn) {
            if (player.getInventory().firstEmpty() != -1) {
                player.getInventory().addItem(item);
            } else {
                player.getWorld().dropItemNaturally(player.getLocation(), item);
            }
        }
    }

    /**
     * 选择最佳队列
     */
    private int selectBestQueue(Player player) {
        int bestQueue = 0;
        int minTasks = Integer.MAX_VALUE;

        for (int i = 0; i < queueButtonSlots.size(); i++) {
            AlchemyTask task = alchemyManager.getTaskManager().getPlayerQueueTask(player.getUniqueId(), i);
            int taskCount = (task != null && !task.isCompleted()) ? 1 : 0;
            if (taskCount < minTasks) {
                minTasks = taskCount;
                bestQueue = i;
            }
        }

        return bestQueue;
    }

    private String replacePlaceholders(String content, AlchemyTask task, int queueIndex) {
        // 队列索引在显示时从1开始，而不是从0开始
        String displayQueueIndex = String.valueOf(queueIndex + 1);

        if (task == null) {
            return content
                    .replace("{queue_index}", displayQueueIndex)
                    .replace("{status}", "空闲")
                    .replace("{remaining_time}", "无");
        }

        return content
                .replace("{queue_index}", displayQueueIndex)
                .replace("{status}", task.isCompleted() ? "空闲" : "运行中")
                .replace("{remaining_time}",
                        alchemyManager.getConfigManager().formatRemainingTime(task.getRemainingTime()));
    }

    /**
     * 重新加载GUI配置
     */
    public void reloadConfiguration() {
        MMOProfessions.getInstance().getLogger().info("正在重新加载炼金GUI配置...");

        // 重新加载配置
        loadConfiguration();

        // 更新所有打开的界面
        for (Map.Entry<UUID, Inventory> entry : playerInventories.entrySet()) {
            UUID playerId = entry.getKey();
            Player player = Bukkit.getPlayer(playerId);

            if (player != null && player.isOnline()) {
                // 关闭当前界面并重新打开
                player.closeInventory();
                openGUI(player);
            }
        }

        MMOProfessions.getInstance().getLogger().info("炼金GUI配置重新加载完成");
    }

}
