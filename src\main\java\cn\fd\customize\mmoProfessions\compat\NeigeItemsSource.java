package cn.fd.customize.mmoProfessions.compat;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.regex.Pattern;

/**
 * NeigeItems物品源实现
 * 处理NeigeItems插件的物品
 */
public class NeigeItemsSource extends AbstractItemSource {

    public NeigeItemsSource(ItemSourceFactory factory, String value, String amount) {
        super(factory, value, amount);
    }

    public NeigeItemsSource(ItemSourceFactory factory, String value) {
        this(factory, value, "1");
    }

    @Override
    public boolean matchesIngredient(ItemStack item) {
        throw new UnsupportedOperationException("Unimplemented method 'matchesIngredient'");
    }

    @Override
    public ItemStack generateOutput(Player player) {
        throw new UnsupportedOperationException("Unimplemented method 'generateOutput'");
    }

}
